# Copyright (c) 2022-2025, The Isaac Lab Project Developers (https://github.com/isaac-sim/IsaacLab/blob/main/CONTRIBUTORS.md).
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

"""LeapHand环境配置文件"""

import math

# 导入基础功能模块
import isaaclab.sim as sim_utils
from isaaclab.assets import ArticulationCfg, AssetBaseCfg  # 资产配置相关
from isaaclab.envs import ManagerBasedRLEnvCfg  # 基础RL环境配置
from isaaclab.managers import EventTermCfg as EventTerm  # 事件管理
from isaaclab.managers import ObservationGroupCfg as ObsGroup  # 观测组配置
from isaaclab.managers import ObservationTermCfg as ObsTerm  # 观测项配置
from isaaclab.managers import RewardTermCfg as RewTerm  # 奖励配置
from isaaclab.managers import SceneEntityCfg  # 场景实体配置
from isaaclab.managers import TerminationTermCfg as DoneTerm  # 终止条件配置
from isaaclab.scene import InteractiveSceneCfg  # 交互场景配置
from isaaclab.utils import configclass  # 配置类装饰器

from . import mdp  # 导入MDP相关函数

##
# 预定义配置
##

# 导入CartPole机器人配置
from isaaclab_assets.robots.cartpole import CARTPOLE_CFG  # isort:skip


##
# 场景定义
##


@configclass
class LeaphandSceneCfg(InteractiveSceneCfg):
    """LeapHand场景配置类"""

    # 地面配置
    ground = AssetBaseCfg(
        prim_path="/World/ground",  # USD场景路径
        spawn=sim_utils.GroundPlaneCfg(size=(100.0, 100.0)),  # 地面大小
    )

    # 机器人配置
    robot: ArticulationCfg = CARTPOLE_CFG.replace(prim_path="{ENV_REGEX_NS}/Robot")

    # 灯光配置
    dome_light = AssetBaseCfg(
        prim_path="/World/DomeLight",  # 灯光USD路径
        spawn=sim_utils.DomeLightCfg(color=(0.9, 0.9, 0.9), intensity=500.0),  # 灯光参数
    )


##
# MDP设置
##


@configclass
class ActionsCfg:
    """动作空间配置"""

    # 关节力矩控制
    joint_effort = mdp.JointEffortActionCfg(
        asset_name="robot",  # 控制目标
        joint_names=["slider_to_cart"],  # 控制关节
        scale=100.0  # 力矩缩放
    )


@configclass
class ObservationsCfg:
    """观测空间配置"""

    @configclass
    class PolicyCfg(ObsGroup):
        """策略网络观测组"""

        # 观测项配置(保持顺序)
        joint_pos_rel = ObsTerm(func=mdp.joint_pos_rel)  # 关节相对位置
        joint_vel_rel = ObsTerm(func=mdp.joint_vel_rel)  # 关节相对速度

        def __post_init__(self) -> None:
            """后初始化配置"""
            self.enable_corruption = False  # 禁用观测损坏
            self.concatenate_terms = True  # 启用观测项拼接

    # 观测组
    policy: PolicyCfg = PolicyCfg()


@configclass
class EventCfg:
    """事件配置"""

    # 重置事件
    reset_cart_position = EventTerm(
        func=mdp.reset_joints_by_offset,  # 重置函数
        mode="reset",  # 触发模式
        params={
            "asset_cfg": SceneEntityCfg("robot", joint_names=["slider_to_cart"]),
            "position_range": (-1.0, 1.0),  # 位置重置范围
            "velocity_range": (-0.5, 0.5),  # 速度重置范围
        },
    )

    reset_pole_position = EventTerm(
        func=mdp.reset_joints_by_offset,
        mode="reset",
        params={
            "asset_cfg": SceneEntityCfg("robot", joint_names=["cart_to_pole"]),
            "position_range": (-0.25 * math.pi, 0.25 * math.pi),  # 杆角度重置范围
            "velocity_range": (-0.25 * math.pi, 0.25 * math.pi),  # 杆角速度重置范围
        },
    )


@configclass
class RewardsCfg:
    """奖励函数配置"""

    # (1) 存活奖励
    alive = RewTerm(func=mdp.is_alive, weight=1.0)
    # (2) 失败惩罚
    terminating = RewTerm(func=mdp.is_terminated, weight=-2.0)
    # (3) 主要任务：保持杆直立
    pole_pos = RewTerm(
        func=mdp.joint_pos_target_l2,
        weight=-1.0,
        params={"asset_cfg": SceneEntityCfg("robot", joint_names=["cart_to_pole"]), "target": 0.0},
    )
    # (4) 形状化奖励：降低小车速度
    cart_vel = RewTerm(
        func=mdp.joint_vel_l1,
        weight=-0.01,
        params={"asset_cfg": SceneEntityCfg("robot", joint_names=["slider_to_cart"])},
    )
    # (5) 形状化奖励：降低杆角速度
    pole_vel = RewTerm(
        func=mdp.joint_vel_l1,
        weight=-0.005,
        params={"asset_cfg": SceneEntityCfg("robot", joint_names=["cart_to_pole"])},
    )


@configclass
class TerminationsCfg:
    """终止条件配置"""

    # (1) 时间限制
    time_out = DoneTerm(func=mdp.time_out, time_out=True)
    # (2) 小车超出边界
    cart_out_of_bounds = DoneTerm(
        func=mdp.joint_pos_out_of_manual_limit,
        params={"asset_cfg": SceneEntityCfg("robot", joint_names=["slider_to_cart"]), "bounds": (-3.0, 3.0)},
    )


##
# 环境配置
##


@configclass
class LeaphandEnvCfg(ManagerBasedRLEnvCfg):
    """LeapHand环境配置类"""

    # 场景配置
    scene: LeaphandSceneCfg = LeaphandSceneCfg(
        num_envs=4096,  # 并行环境数量
        env_spacing=4.0  # 环境间距
    )
    # 基础配置
    observations: ObservationsCfg = ObservationsCfg()  # 观测空间
    actions: ActionsCfg = ActionsCfg()  # 动作空间
    events: EventCfg = EventCfg()  # 事件系统
    # MDP配置
    rewards: RewardsCfg = RewardsCfg()  # 奖励函数
    terminations: TerminationsCfg = TerminationsCfg()  # 终止条件

    # 后初始化配置
    def __post_init__(self) -> None:
        """后初始化配置"""
        # 通用设置
        self.decimation = 2  # 控制步长倍数
        self.episode_length_s = 5  # episode时长(秒)
        # 视角设置
        self.viewer.eye = (8.0, 0.0, 5.0)  # 相机位置
        # 仿真设置
        self.sim.dt = 1 / 120  # 物理仿真步长
        self.sim.render_interval = self.decimation  # 渲染间隔
