# 基于RLGames的手部重定向任务配置

# 参数配置
params:
  seed: 42  # 随机种子设置

  # 环境包装器裁剪参数
  env:
    # 添加到包装器
    clip_observations: 5.0  # 观测值裁剪范围
    # 可以自定义包装器
    clip_actions: 1.0  # 动作值裁剪范围

  # 算法配置
  algo:
    name: a2c_continuous  # 使用连续动作空间的A2C算法

  # 模型配置
  model:
    name: continuous_a2c_logstd  # 连续动作空间的A2C模型，使用log标准差

  # 网络结构配置
  network:
    name: actor_critic  # Actor-Critic网络架构
    separate: False  # Actor和Critic共享网络参数

  # 模型参数配置
    space:  # 动作空间配置
      continuous:  # 连续动作空间配置
        mu_activation: None  # 均值激活函数
        sigma_activation: None  # 标准差激活函数

        mu_init:  # 均值初始化
          name: default  # 均值初始化方法
        sigma_init:  # 标准差初始化
          name: const_initializer  # 标准差初始化方法
          val: 0  # 标准差初始化值
        fixed_sigma: True  # 固定标准差

    mlp:  # 多层感知机配置
      units: [512, 256, 128]  # 隐藏层神经元数量
      activation: elu  # 激活函数
      d2rl: False  # 是否使用D2RL架构

      initializer:  # 权重初始化配置
        name: default  # 权重初始化方法
      regularizer:  # 权重正则化配置
        name: None  # 正则化方法

    rnn:  # 循环神经网络配置
      name: gru  # 使用GRU单元
      units: 256  # GRU隐藏单元数
      layers: 1  # GRU层数
      before_mlp: true  # RNN是否在MLP之前
      concat_input: true  # 是否连接输入
      layer_norm: true  # 是否使用层归一化

  load_checkpoint: False  # 是否加载检查点
  load_path: ''  # 检查点加载路径

  # 训练配置
  config:
    name: leap_hand_reorient  # 任务名称
    env_name: rlgpu  # 环境名称
    device: 'cuda:0'  # 训练设备
    device_name: 'cuda:0'  # 设备名称
    multi_gpu: False  # 是否使用多GPU
    ppo: True  # 使用PPO算法
    mixed_precision: False  # 是否使用混合精度训练
    normalize_input: True  # 是否归一化输入
    normalize_value: True  # 是否归一化价值
    value_bootstrap: True  # 是否使用价值引导
    num_actors: -1  # 并行环境数量，由脚本配置
    reward_shaper:  # 奖励缩放配置
      scale_value: 0.01  # 奖励缩放因子
    normalize_advantage: True  # 是否归一化优势函数
    gamma: 0.99  # 折扣因子
    tau: 0.95  # GAE参数
    learning_rate: 5e-4  # 学习率
    lr_schedule: adaptive  # 学习率调度策略
    schedule_type: standard  # 调度类型
    kl_threshold: 0.02  # KL散度阈值
    score_to_win: 100000  # 获胜分数
    max_epochs: 5000  # 最大训练轮数
    save_best_after: 100  # 多少轮后开始保存最佳模型
    save_frequency: 200  # 保存频率
    print_stats: True  # 是否打印统计信息
    grad_norm: 1.0  # 梯度裁剪范数
    entropy_coef: 0.0  # 熵正则化系数
    truncate_grads: True  # 是否截断梯度
    e_clip: 0.2  # PPO裁剪范围
    horizon_length: 32  # 时序长度
    minibatch_size: 800  # 小批量大小
    mini_epochs: 5  # 每次更新的迭代次数
    critic_coef: 4  # Critic损失系数
    clip_value: True  # 是否裁剪价值
    seq_length: 4  # 序列长度
    bounds_loss_coef: 0.0001  # 边界损失系数

    player:  # 测试配置
      deterministic: True  # 是否使用确定性策略
      games_num: 100000  # 测试游戏次数
      print_stats: True  # 是否打印统计信息
